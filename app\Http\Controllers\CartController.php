<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class CartController extends Controller
{
    /**
     * Display the shopping cart.
     */
    public function index(): View
    {
        $cart = $this->getOrCreateCart();
        $cart->load(['items.product', 'items.productVariant']);
        
        return view('pages.cart.index', compact('cart'));
    }

    /**
     * Add a product to the cart.
     */
    public function add(Request $request)
    {
        \Log::info('Cart add request received', [
            'request_data' => $request->all(),
            'session_id' => Session::getId(),
            'user_id' => Auth::id(),
            'is_authenticated' => Auth::check(),
        ]);

        $request->validate([
            'product_id' => 'required|exists:products,id',
            'variant_id' => 'nullable|exists:product_variants,id',
            'quantity' => 'required|integer|min:1|max:99',
        ]);

        $product = Product::findOrFail($request->product_id);
        $variant = $request->variant_id ? ProductVariant::findOrFail($request->variant_id) : null;

        // Check if product is active and in stock
        if (!$product->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'This product is no longer available.',
            ], 400);
        }

        // Check inventory if tracking is enabled
        if ($product->track_inventory) {
            $availableQuantity = $variant ? $variant->inventory_quantity : $product->inventory_quantity;

            if ($availableQuantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock available.',
                ], 400);
            }
        }

        try {
            $cart = $this->getOrCreateCart();
            \Log::info('Cart retrieved/created', [
                'cart_id' => $cart->id,
                'cart_session_id' => $cart->session_id,
                'cart_user_id' => $cart->user_id,
            ]);

            $cartItem = $cart->addProduct($product, $request->quantity, $variant);
            \Log::info('Product added to cart successfully', [
                'cart_item_id' => $cartItem->id,
                'quantity' => $cartItem->quantity,
                'total' => $cartItem->total,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product added to cart successfully!',
                'cart_count' => $cart->item_count,
                'cart' => [
                    'item_count' => $cart->item_count,
                    'formatted_total' => $cart->formatted_total,
                ],
                'item' => [
                    'id' => $cartItem->id,
                    'name' => $cartItem->name,
                    'quantity' => $cartItem->quantity,
                    'formatted_price' => $cartItem->formatted_price,
                    'formatted_total' => $cartItem->formatted_total,
                ],
            ]);
        } catch (\Exception $e) {
            \Log::error('Error adding product to cart', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding the product to cart.',
            ], 500);
        }
    }

    /**
     * Update cart item quantity.
     */
    public function update(Request $request, $itemId)
    {
        $request->validate([
            'quantity' => 'required|integer|min:0|max:99',
        ]);

        $cart = $this->getOrCreateCart();
        $cartItem = $cart->items()->findOrFail($itemId);

        if ($request->quantity == 0) {
            $cartItem->delete();
            $cart->recalculateTotal();
            
            $freshCart = $cart->fresh();
            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart.',
                'cart' => [
                    'item_count' => $freshCart->item_count,
                    'formatted_total' => $freshCart->formatted_total,
                    'formatted_subtotal' => $freshCart->formatted_subtotal,
                    'formatted_tax_amount' => $freshCart->formatted_tax_amount,
                ],
            ]);
        }

        // Check inventory if tracking is enabled
        $product = $cartItem->product;
        $variant = $cartItem->productVariant;

        if ($product->track_inventory) {
            $availableQuantity = $variant ? $variant->inventory_quantity : $product->inventory_quantity;

            if ($availableQuantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock available.',
                ], 400);
            }
        }

        $cartItem->updateQuantity($request->quantity);
        $cart->recalculateTotal();

        $freshCart = $cart->fresh();
        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully!',
            'cart' => [
                'item_count' => $freshCart->item_count,
                'formatted_total' => $freshCart->formatted_total,
                'formatted_subtotal' => $freshCart->formatted_subtotal,
                'formatted_tax_amount' => $freshCart->formatted_tax_amount,
            ],
            'item' => [
                'id' => $cartItem->id,
                'quantity' => $cartItem->quantity,
                'formatted_total' => $cartItem->formatted_total,
            ],
        ]);
    }

    /**
     * Remove an item from the cart.
     */
    public function remove($itemId)
    {
        $cart = $this->getOrCreateCart();
        $cartItem = $cart->items()->findOrFail($itemId);
        
        $cartItem->delete();
        $cart->recalculateTotal();

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart.',
            'cart' => [
                'item_count' => $cart->fresh()->item_count,
                'formatted_total' => $cart->fresh()->formatted_total,
            ],
        ]);
    }

    /**
     * Clear the entire cart.
     */
    public function clear()
    {
        $cart = $this->getOrCreateCart();
        $cart->clear();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully!',
            'cart' => [
                'item_count' => 0,
                'formatted_total' => 'R0.00',
            ],
        ]);
    }

    /**
     * Get cart count for header display.
     */
    public function count()
    {
        $cart = $this->getOrCreateCart();
        $cart->load('items'); // Ensure items are loaded for item_count calculation

        return response()->json([
            'count' => $cart->item_count,
            'formatted_total' => $cart->formatted_total,
        ]);
    }

    /**
     * Apply a coupon code.
     */
    public function applyCoupon(Request $request)
    {
        $request->validate([
            'coupon_code' => 'required|string|max:50',
        ]);

        $cart = $this->getOrCreateCart();

        if ($cart->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Your cart is empty.',
            ]);
        }

        $couponCode = strtoupper(trim($request->coupon_code));

        // Find the coupon (don't use active scope to allow checking expired coupons)
        $coupon = \App\Models\Coupon::where('code', $couponCode)
            ->where('is_deleted', false)
            ->first();

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid coupon code.',
            ]);
        }

        // Check if coupon is valid
        if (!$coupon->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'This coupon is no longer valid.',
            ]);
        }

        // Check user-specific restrictions
        if (Auth::check() && !$coupon->canBeUsedBy(Auth::user())) {
            return response()->json([
                'success' => false,
                'message' => 'You have already used this coupon the maximum number of times.',
            ]);
        }

        // Prepare cart items for coupon validation
        $cartItems = $cart->items->map(function ($item) {
            return [
                'product_id' => $item->product_id,
                'category_ids' => $item->product->categories->pluck('id')->toArray(),
                'total' => $item->total,
            ];
        })->toArray();

        // Calculate discount
        $discountAmount = $coupon->calculateDiscount($cart->subtotal, $cartItems);

        if ($discountAmount <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'This coupon is not applicable to items in your cart.',
            ]);
        }

        // Apply coupon to cart
        $cart->coupon_id = $coupon->uuid;
        $cart->coupon_code = $coupon->code;
        $cart->discount_amount = $discountAmount;
        $cart->recalculateTotal();

        return response()->json([
            'success' => true,
            'message' => 'Coupon applied successfully!',
            'cart' => [
                'formatted_subtotal' => $cart->formatted_subtotal,
                'formatted_discount_amount' => $cart->formatted_discount_amount,
                'formatted_total' => $cart->formatted_total,
                'coupon_code' => $coupon->code,
                'discount_amount' => $discountAmount,
            ],
        ]);
    }

    /**
     * Remove applied coupon.
     */
    public function removeCoupon(Request $request)
    {
        $cart = $this->getOrCreateCart();

        if (!$cart->coupon_id) {
            return response()->json([
                'success' => false,
                'message' => 'No coupon is currently applied.',
            ]);
        }

        $cart->coupon_id = null;
        $cart->coupon_code = null;
        $cart->discount_amount = 0;
        $cart->recalculateTotal();

        return response()->json([
            'success' => true,
            'message' => 'Coupon removed successfully.',
            'cart' => [
                'formatted_subtotal' => $cart->formatted_subtotal,
                'formatted_discount_amount' => $cart->formatted_discount_amount,
                'formatted_total' => $cart->formatted_total,
            ],
        ]);
    }

    /**
     * Get or create a shopping cart for the current user/session.
     */
    protected function getOrCreateCart(): ShoppingCart
    {
        if (Auth::check()) {
            // For authenticated users, find or create cart by user_id
            $cart = ShoppingCart::active()
                ->where('user_id', Auth::id())
                ->first();
                
            if (!$cart) {
                $cart = ShoppingCart::create([
                    'user_id' => Auth::id(),
                    'currency' => 'ZAR',
                ]);
            }
        } else {
            // For guest users, find or create cart by session_id
            $sessionId = Session::getId();
            $cart = ShoppingCart::active()
                ->where('session_id', $sessionId)
                ->first();
                
            if (!$cart) {
                $cart = ShoppingCart::create([
                    'session_id' => $sessionId,
                    'currency' => 'ZAR',
                ]);
            }
        }

        // Extend cart expiration
        $cart->extendExpiration();
        
        return $cart;
    }

    /**
     * Merge guest cart with user cart after login.
     */
    public function mergeGuestCart()
    {
        if (!Auth::check()) {
            return;
        }

        $sessionId = Session::getId();
        $guestCart = ShoppingCart::active()
            ->where('session_id', $sessionId)
            ->whereNull('user_id')
            ->first();

        if (!$guestCart || $guestCart->isEmpty()) {
            return;
        }

        $userCart = $this->getOrCreateCart();

        // Merge guest cart items into user cart
        foreach ($guestCart->items as $guestItem) {
            $userCart->addProduct(
                $guestItem->product,
                $guestItem->quantity,
                $guestItem->productVariant
            );
        }

        // Delete guest cart
        $guestCart->delete();
    }
}
