<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Test database connection
try {
    \Illuminate\Support\Facades\DB::connection()->getPdo();
    echo "Database connection: OK\n";
} catch (Exception $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test if roles exist
try {
    $roles = \App\Models\Role::all();
    echo "Roles in database: " . $roles->count() . "\n";
    foreach ($roles as $role) {
        echo "  - {$role->name} (ID: {$role->id})\n";
    }
} catch (Exception $e) {
    echo "Error fetching roles: " . $e->getMessage() . "\n";
}

// Test creating a user with role
try {
    $adminRole = \App\Models\Role::where('name', 'admin')->first();
    if (!$adminRole) {
        echo "Admin role not found, creating...\n";
        $adminRole = \App\Models\Role::create([
            'name' => 'admin',
            'slug' => 'admin',
            'description' => 'Administrator role',
            'permissions' => [
                'users' => ['create', 'read', 'update', 'delete'],
                'projects' => ['create', 'read', 'update', 'delete'],
            ],
            'is_active' => true,
        ]);
    }
    
    $user = \App\Models\User::factory()->create(['role_id' => $adminRole->id]);
    echo "Created test user: {$user->email} with role: {$user->role->name}\n";
    
    // Test role methods
    echo "isAdmin(): " . ($user->isAdmin() ? 'true' : 'false') . "\n";
    echo "isAdminOrStaff(): " . ($user->isAdminOrStaff() ? 'true' : 'false') . "\n";
    
} catch (Exception $e) {
    echo "Error creating test user: " . $e->getMessage() . "\n";
}

echo "Debug complete.\n";
