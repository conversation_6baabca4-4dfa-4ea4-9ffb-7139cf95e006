<?php

namespace Tests\Feature\Admin;

use App\Models\Project;
use App\Models\Service;
use App\Models\User;
use App\Models\Role;
use App\Services\ActivityLogger;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProjectControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;
    protected User $client;
    protected Service $service;
    protected Project $project;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $adminRole = Role::factory()->admin()->create();
        $clientRole = Role::factory()->client()->create();

        // Create users
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->client = User::factory()->create(['role_id' => $clientRole->id]);

        // Create service
        $this->service = Service::factory()->create();

        // Create project
        $this->project = Project::factory()->create([
            'client_id' => $this->client->id,
            'service_id' => $this->service->id,
        ]);

        // Mock services with expectations
        $this->mock(ActivityLogger::class, function ($mock) {
            $mock->shouldReceive('logCustomerActivity')->andReturn(
                \App\Models\ActivityLog::factory()->make()
            );
        });

        $this->mock(ImageService::class, function ($mock) {
            $mock->shouldReceive('processUploadedImage')->andReturn([
                'success' => true,
                'original_path' => 'test-path.jpg'
            ]);
            $mock->shouldReceive('deleteImage')->andReturn(true);
            $mock->shouldReceive('getImageUrl')->andReturn('https://via.placeholder.com/300x200');
        });
    }

    /** @test */
    public function admin_can_view_projects_index()
    {
        $this->actingAs($this->admin)
            ->get(route('admin.projects.index'))
            ->assertStatus(200)
            ->assertViewIs('admin.projects.index')
            ->assertViewHas('projects')
            ->assertSee($this->project->title);
    }

    /** @test */
    public function non_admin_cannot_access_projects_index()
    {
        // Create a customer role first
        $customerRole = Role::factory()->customer()->create();

        // Create a user with customer role
        $customer = User::factory()->create([
            'role_id' => $customerRole->id
        ]);

        $this->actingAs($customer)
            ->get(route('admin.projects.index'))
            ->assertStatus(403);
    }

    /** @test */
    public function admin_can_view_project_details()
    {
        $this->actingAs($this->admin)
            ->get(route('admin.projects.show', $this->project))
            ->assertStatus(200)
            ->assertViewIs('admin.projects.show')
            ->assertViewHas('project')
            ->assertSee($this->project->title)
            ->assertSee($this->project->description);
    }

    /** @test */
    public function admin_can_view_project_edit_form()
    {
        $this->actingAs($this->admin)
            ->get(route('admin.projects.edit', $this->project))
            ->assertStatus(200)
            ->assertViewIs('admin.projects.edit')
            ->assertViewHas(['project', 'services', 'clients', 'statuses'])
            ->assertSee($this->project->title);
    }

    /** @test */
    public function admin_can_update_project_basic_information()
    {
        $updateData = [
            'title' => 'Updated Project Title',
            'description' => 'Updated project description',
            'content' => 'Updated detailed content',
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'service_id' => $this->service->id,
            'status' => 'in_progress',
            'priority' => 'high',
            'is_published' => true,
            'is_featured' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData);

        // Refresh the project to get the updated slug
        $this->project->refresh();

        $response->assertRedirect(route('admin.projects.show', $this->project))
            ->assertSessionHas('success');

        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'title' => 'Updated Project Title',
            'description' => 'Updated project description',
            'status' => 'in_progress',
            'priority' => 'high',
            'is_published' => true,
            'is_featured' => false,
        ]);
    }

    /** @test */
    public function admin_can_update_project_with_time_and_budget()
    {
        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'estimated_hours' => 100.50,
            'actual_hours' => 95.25,
            'hourly_rate' => 75.00,
            'total_amount' => 7143.75,
            'currency_code' => 'ZAR',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertRedirect(route('admin.projects.show', $this->project));

        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'estimated_hours' => 100.50,
            'actual_hours' => 95.25,
            'hourly_rate' => 75.00,
            'total_amount' => 7143.75,
            'currency_code' => 'ZAR',
        ]);
    }

    /** @test */
    public function admin_can_update_project_with_dates()
    {
        $startDate = now()->subDays(30)->format('Y-m-d');
        $endDate = now()->format('Y-m-d');

        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertRedirect(route('admin.projects.show', $this->project));

        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'start_date' => $startDate . ' 00:00:00',
            'end_date' => $endDate . ' 00:00:00',
        ]);
    }

    /** @test */
    public function admin_can_update_project_seo_information()
    {
        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'meta_title' => 'SEO Optimized Title',
            'meta_description' => 'SEO optimized description for search engines',
            'meta_keywords' => 'seo, optimization, project, keywords',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertRedirect(route('admin.projects.show', $this->project));

        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'meta_title' => 'SEO Optimized Title',
            'meta_description' => 'SEO optimized description for search engines',
            'meta_keywords' => 'seo, optimization, project, keywords',
        ]);
    }

    /** @test */
    public function project_slug_is_updated_when_title_changes()
    {
        $originalSlug = $this->project->slug;

        $updateData = [
            'title' => 'Completely New Project Title',
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertRedirect();

        $this->project->refresh();
        $this->assertNotEquals($originalSlug, $this->project->slug);
        $this->assertEquals('completely-new-project-title', $this->project->slug);
    }

    /** @test */
    public function project_update_validates_required_fields()
    {
        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), [])
            ->assertSessionHasErrors(['title', 'description', 'client_name', 'status']);
    }

    /** @test */
    public function project_update_validates_date_logic()
    {
        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'start_date' => now()->format('Y-m-d'),
            'end_date' => now()->subDays(10)->format('Y-m-d'), // End date before start date
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['end_date']);
    }

    /** @test */
    public function project_update_validates_numeric_fields()
    {
        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'estimated_hours' => 'not-a-number',
            'actual_hours' => -10,
            'hourly_rate' => 'invalid',
            'total_amount' => 'not-numeric',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['estimated_hours', 'actual_hours', 'hourly_rate', 'total_amount']);
    }

    /** @test */
    public function project_update_validates_status_values()
    {
        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'invalid_status',
            'priority' => 'invalid_priority',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['status', 'priority']);
    }

    /** @test */
    public function admin_can_delete_project()
    {
        $this->actingAs($this->admin)
            ->delete(route('admin.projects.destroy', $this->project))
            ->assertRedirect(route('admin.projects.index'))
            ->assertSessionHas('success');

        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'is_deleted' => true,
        ]);
    }

    /** @test */
    public function projects_index_can_be_filtered_by_service()
    {
        $anotherService = Service::factory()->create(['name' => 'Another Service']);
        $anotherProject = Project::factory()->create(['service_id' => $anotherService->id]);

        $this->actingAs($this->admin)
            ->get(route('admin.projects.index', ['service_id' => $this->service->id]))
            ->assertStatus(200)
            ->assertSee($this->project->title)
            ->assertDontSee($anotherProject->title);
    }

    /** @test */
    public function projects_index_can_be_filtered_by_status()
    {
        $completedProject = Project::factory()->create(['status' => 'completed']);
        $inProgressProject = Project::factory()->create(['status' => 'in_progress']);

        $this->actingAs($this->admin)
            ->get(route('admin.projects.index', ['status' => 'completed']))
            ->assertStatus(200)
            ->assertSee($completedProject->title)
            ->assertDontSee($inProgressProject->title);
    }

    /** @test */
    public function projects_index_can_be_searched()
    {
        $searchableProject = Project::factory()->create(['title' => 'Unique Searchable Project']);
        $otherProject = Project::factory()->create(['title' => 'Different Project']);

        $this->actingAs($this->admin)
            ->get(route('admin.projects.index', ['search' => 'Unique Searchable']))
            ->assertStatus(200)
            ->assertSee($searchableProject->title)
            ->assertDontSee($otherProject->title);
    }

    /** @test */
    public function admin_can_update_project_with_featured_image()
    {
        Storage::fake('public');

        // Set up project with existing featured image so deleteImage will be called
        $this->project->update(['featured_image' => 'projects/old-image.jpg']);

        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('deleteImage')
            ->once()
            ->with('projects/old-image.jpg', true)
            ->andReturn(true);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => true,
                'original_path' => 'projects/test-image.jpg'
            ]);

        $file = UploadedFile::fake()->image('project.jpg', 1200, 800);

        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertRedirect(route('admin.projects.show', $this->project));

        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'featured_image' => 'projects/test-image.jpg',
        ]);
    }

    /** @test */
    public function project_update_validates_image_file_type()
    {
        Storage::fake('public');

        $file = UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf');

        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['featured_image']);
    }

    /** @test */
    public function project_update_validates_image_file_size()
    {
        Storage::fake('public');

        $file = UploadedFile::fake()->image('large-image.jpg')->size(6000); // 6MB

        $updateData = [
            'title' => $this->project->title,
            'description' => $this->project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['featured_image']);
    }

    /** @test */
    public function admin_can_toggle_project_published_status()
    {
        $this->project->update(['is_published' => false]);

        $this->actingAs($this->admin)
            ->post(route('admin.projects.toggle-published', $this->project))
            ->assertJson(['success' => true]);

        $this->assertTrue($this->project->fresh()->is_published);
    }

    /** @test */
    public function admin_can_toggle_project_featured_status()
    {
        $this->project->update(['is_featured' => false]);

        $this->actingAs($this->admin)
            ->post(route('admin.projects.toggle-featured', $this->project))
            ->assertJson(['success' => true]);

        $this->assertTrue($this->project->fresh()->is_featured);
    }

    /** @test */
    public function non_admin_cannot_toggle_project_status()
    {
        // Create a customer role first
        $customerRole = Role::factory()->customer()->create();

        // Create a user with customer role
        $customer = User::factory()->create([
            'role_id' => $customerRole->id
        ]);

        $this->actingAs($customer)
            ->post(route('admin.projects.toggle-published', $this->project))
            ->assertStatus(403);

        $this->actingAs($customer)
            ->post(route('admin.projects.toggle-featured', $this->project))
            ->assertStatus(403);
    }

    /** @test */
    public function project_update_handles_missing_client_name_gracefully()
    {
        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'client_id' => $this->client->id,
            // Missing client_name
            'status' => 'completed',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['client_name']);
    }

    /** @test */
    public function project_update_handles_invalid_client_id()
    {
        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'client_id' => 99999, // Non-existent client
            'client_name' => 'Test Client',
            'status' => 'completed',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['client_id']);
    }

    /** @test */
    public function project_update_handles_invalid_service_id()
    {
        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'client_id' => $this->client->id,
            'client_name' => 'Test Client',
            'service_id' => 99999, // Non-existent service
            'status' => 'completed',
        ];

        $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $this->project), $updateData)
            ->assertSessionHasErrors(['service_id']);
    }
}
