<?php

namespace Tests\Feature;

use App\Mail\OrderConfirmation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class EmailIntegrationTestSimple extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_run_simple_email_test()
    {
        Mail::fake();
        
        $user = User::factory()->create();
        
        // This should work without hanging
        $this->assertTrue(true);
    }
}
